import asyncio
import sys
import json
import time
import websockets
import os
from puter import Puter<PERSON>lient

def load_env_file(filepath='.env'):
    """Load environment variables from a .env file"""
    env_vars = {}
    if os.path.exists(filepath):
        with open(filepath, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
    return env_vars

class ChatCLI:
    def __init__(self):
        # Load API key from environment file
        env_vars = load_env_file()
        api_key = env_vars.get('APIKEY')
        if not api_key:
            raise ValueError("APIKEY not found in .env file")

        self.client = PuterClient(api_key=api_key)
        self.messages = []
        self.pending_responses = {}
        self.authenticated = False
        self.current_response_text = ""
        self.current_request_id = None

    async def setup(self):
        print("starting puter client...")
        if not self.client.build_ws():
            print("failed to connect to puter")
            return False

        print("waiting for authentication...")
        # wait for auth status
        await self.wait_for_auth()

        if not self.authenticated:
            print("authentication failed")
            return False

        print("authenticated! type 'exit' to quit\n")
        return True

    async def wait_for_auth(self):
        if not self.client.connected_clients:
            return

        await asyncio.sleep(2)
        self.authenticated = True  # assume auth worked since we got this far

    async def send_chat_request(self, messages):
        if not self.client.connected_clients:
            raise Exception("no connected clients")

        # generate unique request id
        request_id = str(time.time())
        chat_request = {
            "type": "chat_request",
            "request_id": request_id,
            "messages": messages,
            "model": "claude-sonnet-4",
            "stream": True
        }

        # create response queue for this request
        response_queue = asyncio.Queue()
        self.pending_responses[request_id] = response_queue

        # send to first connected client
        client = next(iter(self.client.connected_clients))
        await client.send(json.dumps(chat_request))

        return request_id, response_queue

    async def listen_for_responses(self):
        """background task to listen for websocket messages"""
        if not self.client.connected_clients:
            return

        client = next(iter(self.client.connected_clients))
        try:
            while True:
                try:
                    # Use recv() instead of async for loop
                    message = await client.recv()
                    data = json.loads(message)

                    # remove debug output for normal chat messages
                    if data.get('type') not in ['chat_chunk', 'chat_complete', 'chat_error']:
                        print(f"python received: {data.get('type')} - {data}")

                    # Handle streaming responses directly
                    if data.get("type") == "chat_chunk" and data.get("request_id") == self.current_request_id:
                        chunk = data.get("text", "")
                        print(chunk, end="", flush=True)
                        self.current_response_text += chunk
                    elif data.get("type") == "chat_complete" and data.get("request_id") == self.current_request_id:
                        print("\n")
                        # Add to message history
                        if self.current_response_text:
                            self.messages.append({
                                "role": "assistant",
                                "content": self.current_response_text
                            })
                        # Reset for next request
                        self.current_request_id = None
                        self.current_response_text = ""
                    elif data.get("type") == "chat_error" and data.get("request_id") == self.current_request_id:
                        print(f"error: {data.get('error')}")
                        self.current_request_id = None
                        self.current_response_text = ""

                    # Also put in queue for any other processing
                    request_id = data.get("request_id")
                    if request_id in self.pending_responses:
                        await self.pending_responses[request_id].put(data)
                        if data.get("type") in ["chat_complete", "chat_error"]:
                            await self.pending_responses[request_id].put(None)  # signal end

                except websockets.exceptions.ConnectionClosed:
                    print("websocket connection closed")
                    break
                except Exception as e:
                    print(f"listener error: {e}")
                    break

        except Exception as e:
            print(f"listener setup error: {e}")

    async def chat_loop(self):
        # start background listener BEFORE any chat requests
        listener_task = asyncio.create_task(self.listen_for_responses())
        # give it a moment to start
        await asyncio.sleep(0.1)

        try:
            while True:
                try:
                    user_input = await asyncio.get_event_loop().run_in_executor(
                        None, lambda: input("> ").strip()
                    )

                    if user_input.lower() == 'exit':
                        break

                    if not user_input:
                        continue

                    # add user message to memory
                    self.messages.append({
                        "role": "user",
                        "content": user_input
                    })

                    print("assistant: ", end="", flush=True)

                    # Set up for streaming response
                    self.current_response_text = ""

                    # send chat request
                    request_id, _ = await self.send_chat_request(self.messages)
                    self.current_request_id = request_id

                    # Wait for completion signal from the listener
                    while self.current_request_id is not None:
                        await asyncio.sleep(0.1)

                    # cleanup this request
                    if request_id in self.pending_responses:
                        del self.pending_responses[request_id]

                except KeyboardInterrupt:
                    print("\ninterrupted")
                    break
                except Exception as e:
                    print(f"error: {e}")
        finally:
            listener_task.cancel()

    def cleanup(self):
        self.client.close()
        print("goodbye!")

async def main():
    chat = ChatCLI()

    try:
        if await chat.setup():
            await chat.chat_loop()
    finally:
        chat.cleanup()

if __name__ == "__main__":
    asyncio.run(main())