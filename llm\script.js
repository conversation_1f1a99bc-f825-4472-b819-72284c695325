const startTime = Date.now();
let isAuthenticated = false;

const ws = new WebSocket('ws://localhost:8635');

ws.onopen = function() {
    ws.send(JSON.stringify({
        type: 'status',
        message: 'startup done',
        uptime: Date.now() - startTime
    }));
    
    // sign in immediately
    signInUser();
};

async function signInUser() {
    try {
        //await puter.ui.authenticateWithPuter();
        setTimeout(() => {
            isAuthenticated = puter.auth.isSignedIn();
            ws.send(JSON.stringify({
                type: 'auth_status',
                authenticated: isAuthenticated
            }));
        }, 2000);
    } catch (error) {
        ws.send(JSON.stringify({
            type: 'auth_error',
            error: error.message
        }));
    }
}

ws.onmessage = async function(event) {
    const data = JSON.parse(event.data);
    console.log('received:', data);

    if (data.type === 'health_check') {
        const uptime = Date.now() - startTime;
        ws.send(JSON.stringify({
            type: 'health_response',
            status: 'OK',
            uptime: uptime,
            message: `running for ${uptime}ms`
        }));
    }

    if (data.type === 'chat_request') {
        if (!puter.auth.isSignedIn()) {
            ws.send(JSON.stringify({
                type: 'chat_error',
                request_id: data.request_id,
                error: 'not authenticated'
            }));
            return;
        }
    
        try {
            const response = await puter.ai.chat(data.messages, {
                model: data.model,
                stream: data.stream
            });
    
            if (data.stream) {
                for await (const part of response) {
                    console.log('streaming part:', part);
                    const text = part?.text || part?.content || '';
                    if (text) {
                        const message = {
                            type: 'chat_chunk',
                            request_id: data.request_id,
                            text: text
                        };
                        console.log('sending to python:', message); // debug log
                        ws.send(JSON.stringify(message));
                    }
                }
                const completeMessage = {
                    type: 'chat_complete',
                    request_id: data.request_id
                };
                console.log('sending complete:', completeMessage); // debug log
                ws.send(JSON.stringify(completeMessage));
            }
        } catch (error) {
            console.log('chat error:', error); // debug log
            ws.send(JSON.stringify({
                type: 'chat_error',
                request_id: data.request_id,
                error: error.message
            }));
        }
    }
};

ws.onclose = function() {
    console.log('connection closed');
};

ws.onerror = function(error) {
    console.log('oops:', error);
};